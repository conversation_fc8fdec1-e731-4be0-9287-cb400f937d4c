package com.yunqu.crawler.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.domain.XtyTariffCrawlRecordLib;
import com.yunqu.crawler.domain.XtyTariffRecord;
import com.yunqu.crawler.domain.bo.XtyCrawlerTaskBo;
import com.yunqu.crawler.domain.vo.XtyCrawlerTaskVo;
import com.yunqu.crawler.event.CrawlerTaskEvent;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordLibMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.mapper.XtyTariffRecordMapper;
import com.yunqu.crawler.service.IXtyCrawlerTaskService;
import com.yunqu.emergency.common.core.utils.MapstructUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 资费爬取任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class XtyCrawlerTaskServiceImpl extends ServiceImpl<XtyCrawlerTaskMapper, XtyCrawlerTask> implements IXtyCrawlerTaskService {

    private final XtyTariffCrawlRecordMapper recordMapper;
    private final XtyTariffCrawlRecordLibMapper xtyTariffCrawlRecordLibMapper;
    private final XtyTariffRecordMapper xtyTariffRecordMapper;


    @Async
    @EventListener
    public void reanalysisTaskData(CrawlerTaskEvent event) {
        /*if (event == null) return;
        Long taskId = event.getTaskId();
        if (taskId == null) return;
        log.info("执行监听任务: {}", taskId);
        XtyCrawlerTask task = baseMapper.selectById(taskId);
        if (task == null) return;

        int pageIndex = 1;
        int pageSize = 100;


        String nowMonth = DateUtil.format(DateUtils.getNowDate(), "yyyyMM");
        String nowDate = DateUtil.format(DateUtils.getNowDate(), "yyyyMMdd");
        int dateId = Convert.toInt(nowDate);
        int monthId = Convert.toInt(nowMonth);

        while (true) {
            Page<XtyTariffCrawlRecord> page = new Page<>(pageIndex, pageSize);
            LambdaQueryWrapper<XtyTariffCrawlRecord> queryWrapper = Wrappers.lambdaQuery(XtyTariffCrawlRecord.class)
                    .eq(XtyTariffCrawlRecord::getTaskId, taskId)
                    .orderByAsc(XtyTariffCrawlRecord::getId);

            Page<XtyTariffCrawlRecord> resultPage = recordMapper.selectPage(page, queryWrapper);

            if (resultPage.getRecords().isEmpty()) {
                break; // 如果没有更多数据，退出循环
            }

            // 处理当前页的数据
            List<XtyTariffCrawlRecord> records = resultPage.getRecords();
            // TODO: 在这里处理records数据
            recordToLib(records, monthId, dateId);
            pageIndex++; // 移动到下一页
        }*/
    }


    private void recordToLib(List<XtyTariffCrawlRecord> records, int monthId, int dateId) {
        for (XtyTariffCrawlRecord record : records) {
            String tariffNo = record.getTariffNo();
            String tariffName = record.getName();
            LambdaQueryWrapper<XtyTariffCrawlRecordLib> wrapper = Wrappers.lambdaQuery(XtyTariffCrawlRecordLib.class).eq(StringUtils.isNotBlank(tariffNo), XtyTariffCrawlRecordLib::getTariffNo, tariffNo)
                    .eq(StringUtils.isNotBlank(tariffName), XtyTariffCrawlRecordLib::getName, tariffName);
            XtyTariffCrawlRecordLib recordLib = xtyTariffCrawlRecordLibMapper.selectOne(wrapper);
            XtyTariffRecord tariffRecord = null;
            if (StringUtils.isNotBlank(tariffNo)) {
                Wrapper<XtyTariffRecord> recordLambdaQueryWrapper = Wrappers.lambdaQuery(XtyTariffRecord.class).eq(XtyTariffRecord::getReportNo, tariffNo);
                tariffRecord = xtyTariffRecordMapper.selectOne(recordLambdaQueryWrapper);
            }
            if (recordLib == null) {
                recordLib = BeanUtil.copyProperties(record, XtyTariffCrawlRecordLib.class, "id", "createTime", "updateTime", "dateId");
                if (tariffRecord != null) {
                    recordLib.setTariffRecordId(tariffRecord.getId());
                    recordLib.setReported(1);
                } else {
                    recordLib.setReported(0);
                }
                recordLib.setDateId(dateId);
                recordLib.setMonthId(monthId);
                xtyTariffCrawlRecordLibMapper.insert(recordLib);
            } else {
                recordLib = BeanUtil.copyProperties(record, XtyTariffCrawlRecordLib.class, "id", "createTime", "updateTime", "dateId");
                if (tariffRecord != null) {
                    recordLib.setTariffRecordId(tariffRecord.getId());
                    recordLib.setReported(1);
                } else {
                    recordLib.setReported(0);
                }
                recordLib.setDateId(dateId);
                recordLib.setMonthId(monthId);
                xtyTariffCrawlRecordLibMapper.updateById(recordLib);
            }

        }
    }


    /**
     * 查询资费爬取任务
     *
     * @param id 主键
     * @return 资费爬取任务
     */
    @Override
    public XtyCrawlerTaskVo queryById(Long id) {
        log.info("开始查询资费爬取任务, id: {}", id);
        XtyCrawlerTaskVo result = baseMapper.selectVoById(id);
        log.info("完成查询资费爬取任务, id: {}, 结果: {}", id, result != null ? "存在" : "不存在");
        return result;
    }

    /**
     * 分页查询资费爬取任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资费爬取任务分页列表
     */
    @Override
    public TableDataInfo<XtyCrawlerTaskVo> queryPageList(XtyCrawlerTaskBo bo, PageQuery pageQuery) {
        log.info("开始分页查询资费爬取任务, 查询条件: {}, 分页参数: {}", bo, pageQuery);
        long startTime = System.currentTimeMillis();

        LambdaQueryWrapper<XtyCrawlerTask> lqw = buildQueryWrapper(bo);
        Page<XtyCrawlerTaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        long endTime = System.currentTimeMillis();
        log.info("完成分页查询资费爬取任务, 耗时: {}ms, 总记录数: {}", endTime - startTime, result.getTotal());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的资费爬取任务列表
     *
     * @param bo 查询条件
     * @return 资费爬取任务列表
     */
    @Override
    public List<XtyCrawlerTaskVo> queryList(XtyCrawlerTaskBo bo) {
        log.info("开始查询资费爬取任务列表, 查询条件: {}", bo);
        long startTime = System.currentTimeMillis();

        LambdaQueryWrapper<XtyCrawlerTask> lqw = buildQueryWrapper(bo);
        List<XtyCrawlerTaskVo> result = baseMapper.selectVoList(lqw);

        long endTime = System.currentTimeMillis();
        log.info("完成查询资费爬取任务列表, 耗时: {}ms, 结果数量: {}", endTime - startTime, result.size());
        return result;
    }

    private LambdaQueryWrapper<XtyCrawlerTask> buildQueryWrapper(XtyCrawlerTaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<XtyCrawlerTask> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(XtyCrawlerTask::getId);
        lqw.eq(bo.getDateId() != null, XtyCrawlerTask::getDateId, bo.getDateId());
        lqw.eq(StringUtils.isNotBlank(bo.getProvinceCode()), XtyCrawlerTask::getProvinceCode, bo.getProvinceCode());
        lqw.eq(bo.getEntType() != null, XtyCrawlerTask::getEntType, bo.getEntType());
        lqw.eq(bo.getStatus() != null, XtyCrawlerTask::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增资费爬取任务
     *
     * @param bo 资费爬取任务
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(XtyCrawlerTaskBo bo) {
        log.info("开始新增资费爬取任务, 任务信息: {}", bo);
        XtyCrawlerTask add = MapstructUtils.convert(bo, XtyCrawlerTask.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            log.info("完成新增资费爬取任务, id: {}", add.getId());
        } else {
            log.warn("新增资费爬取任务失败");
        }
        return flag;
    }

    /**
     * 修改资费爬取任务
     *
     * @param bo 资费爬取任务
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(XtyCrawlerTaskBo bo) {
        log.info("开始修改资费爬取任务, 任务信息: {}", bo);
        XtyCrawlerTask update = MapstructUtils.convert(bo, XtyCrawlerTask.class);
        validEntityBeforeSave(update);
        boolean result = baseMapper.updateById(update) > 0;
        log.info("完成修改资费爬取任务, id: {}, 结果: {}", update.getId(), result ? "成功" : "失败");
        return result;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(XtyCrawlerTask entity) {
        // TODO: 实现具体的校验逻辑
        log.debug("执行任务保存前校验, 任务信息: {}", entity);
    }

    /**
     * 校验并批量删除资费爬取任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("开始批量删除资费爬取任务, ids: {}, 是否校验: {}", ids, isValid);
        boolean result = baseMapper.deleteByIds(ids) > 0;
        log.info("完成批量删除资费爬取任务, 结果: {}", result ? "成功" : "失败");
        return result;
    }

    /**
     * 检查是否有任务正在运行
     *
     * @return 是否有任务正在运行
     */
    @Override
    public boolean checkAnyTaskIsRunning(String serverName) {
        log.debug("检查是否有任务正在运行");
        LambdaUpdateWrapper<XtyCrawlerTask> wrapper = Wrappers.lambdaUpdate(XtyCrawlerTask.class)
                .eq(XtyCrawlerTask::getRunServer, serverName)
                .eq(XtyCrawlerTask::getStatus, 1);
        boolean result = baseMapper.exists(wrapper);
        log.info("检查任务运行状态完成, 是否有运行中的任务: {}", result);
        return result;
    }

    /**
     * 检查是否有任务正在运行
     *
     * @return 是否有任务正在运行
     */
    @Override
    public boolean checkAnyTaskIsRunning(List<String> operators, String serverName) {
        log.debug("检查是否有任务正在运行");
        LambdaUpdateWrapper<XtyCrawlerTask> wrapper = Wrappers.lambdaUpdate(XtyCrawlerTask.class).in(XtyCrawlerTask::getOperatorCode, operators)
                .eq(XtyCrawlerTask::getRunServer, serverName)
                .eq(XtyCrawlerTask::getStatus, 1);
        boolean result = baseMapper.exists(wrapper);
        log.info("检查任务运行状态完成, 是否有运行中的任务: {}", result);
        return result;
    }

    /**
     * 获取一个任务
     *
     * @return 任务
     */
    @Lock4j(name = "crawler:task:take")
    @Override
    public XtyCrawlerTask take(List<String> operators, String versionNo, String serverName) {
        log.info("开始获取待执行任务");
        LambdaQueryWrapper<XtyCrawlerTask> wrapper = Wrappers.lambdaQuery(XtyCrawlerTask.class).in(XtyCrawlerTask::getOperatorCode, operators)
                .eq(XtyCrawlerTask::getVersionNo, versionNo)
                .eq(XtyCrawlerTask::getStatus, 0).orderByAsc(XtyCrawlerTask::getEntType).orderByAsc(XtyCrawlerTask::getId)
                .last("limit 1");

        XtyCrawlerTask xtyCrawlerTask = baseMapper.selectOne(wrapper);
        if (xtyCrawlerTask != null) {
            log.info("获取到待执行任务, id: {}, 开始更新状态", xtyCrawlerTask.getId());
            xtyCrawlerTask.setStatus(1);
            xtyCrawlerTask.setRunServer(serverName);
            baseMapper.updateById(xtyCrawlerTask);
            log.info("任务状态更新完成, id: {}", xtyCrawlerTask.getId());
        } else {
            log.info("当前没有待执行的任务");
        }
        return xtyCrawlerTask;
    }

    /**
     * 检查任务是否正在运行
     *
     * @param taskId 任务id
     * @return 是否正在运行
     */
    @Override
    public boolean checkTaskIsRunning(Long taskId) {
        log.debug("检查任务运行状态, taskId: {}", taskId);
        LambdaQueryWrapper<XtyCrawlerTask> wrapper = Wrappers.lambdaQuery(XtyCrawlerTask.class)
                .eq(XtyCrawlerTask::getId, taskId)
                .eq(XtyCrawlerTask::getStatus, Constants.CRAWLER_TASK_STATUS_1);
        boolean result = baseMapper.exists(wrapper);
        log.info("任务运行状态检查完成, taskId: {}, 是否运行中: {}", taskId, result);
        return result;
    }

    @Override
    public boolean updateById(Long taskId, Integer status, String memo) {
        LambdaUpdateWrapper<XtyCrawlerTask> wrapper = Wrappers.lambdaUpdate(XtyCrawlerTask.class).set(XtyCrawlerTask::getMemo, memo)
                .set(XtyCrawlerTask::getStatus, status)
                .eq(XtyCrawlerTask::getId, taskId);
        return baseMapper.update(wrapper) > 0;
    }

    /**
     * 检查指定日期和版本是否已存在任务
     * 用于幂等性检查，避免重复创建任务
     *
     * @param dateId 日期ID
     * @param versionNo 版本号
     * @return 是否已存在任务
     */
    @Override
    public boolean existsTasksByDateAndVersion(Integer dateId, String versionNo) {
        log.debug("检查任务是否已存在, dateId: {}, versionNo: {}", dateId, versionNo);
        long startTime = System.currentTimeMillis();

        LambdaQueryWrapper<XtyCrawlerTask> wrapper = Wrappers.lambdaQuery(XtyCrawlerTask.class)
                .eq(XtyCrawlerTask::getDateId, dateId)
                .eq(XtyCrawlerTask::getVersionNo, versionNo)
                .last("LIMIT 1");

        boolean exists = baseMapper.exists(wrapper);
        long endTime = System.currentTimeMillis();

        log.info("任务存在性检查完成, dateId: {}, versionNo: {}, 是否存在: {}, 耗时: {}ms",
                dateId, versionNo, exists, endTime - startTime);
        return exists;
    }
}
